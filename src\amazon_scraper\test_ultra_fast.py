#!/usr/bin/env python3
"""
Ultra-fast mode test - should be much faster than before
"""

import logging
import sys
import time
from amazon_scraper.scraper import AmazonScraper

# Configure minimal logging for speed
logging.basicConfig(
    level=logging.WARNING,  # Only show warnings and errors
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_ultra_fast():
    """Test the ultra-fast scraper mode"""
    print("🚀 Testing ULTRA-FAST MODE")
    print("- No proxy (regular selenium)")
    print("- Fast product parsing")
    print("- Batch price checking")
    print("- Minimal logging")
    print("-" * 50)
    
    scraper = AmazonScraper(use_proxy=False)  # Ultra-fast mode
    test_url = "https://www.amazon.it/s?k=laptop&ref=nb_sb_noss"
    
    try:
        start_time = time.time()
        products = scraper.scrape_amazon_page(test_url, max_pages=1)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if products:
            print(f"✅ Ultra-fast mode: Found {len(products)} products")
            print(f"⏱️  Time: {duration:.2f} seconds")
            print(f"📊 Speed: {len(products)/duration:.1f} products/second")
            
            # Show first few products
            print("\nFirst few products:")
            for i, product in enumerate(products[:5]):
                print(f"{i+1}. {product.title[:60]}... - {product.price}")
            
            # Performance analysis
            print(f"\n📈 PERFORMANCE ANALYSIS:")
            print(f"⚡ Processing rate: {len(products)/duration:.1f} products/second")
            
            if duration < 30:
                print("🎉 EXCELLENT! Under 30 seconds")
            elif duration < 60:
                print("✅ GOOD! Under 1 minute")
            elif duration < 120:
                print("⚠️  ACCEPTABLE! Under 2 minutes")
            else:
                print("❌ SLOW! Over 2 minutes")
                
            return duration, len(products)
        else:
            print("❌ No products found")
            return None, 0
            
    except Exception as e:
        print(f"❌ Ultra-fast mode failed: {str(e)}")
        return None, 0

def main():
    print("Amazon Scraper Ultra-Fast Mode Test")
    print("=" * 50)
    
    # Test ultra-fast mode
    fast_time, fast_products = test_ultra_fast()
    
    print("\n" + "=" * 50)
    print("🎯 RESULTS SUMMARY")
    print("=" * 50)
    
    if fast_time:
        print(f"✅ Success: {fast_products} products in {fast_time:.2f} seconds")
        print(f"📊 Rate: {fast_products/fast_time:.1f} products/second")
        
        # Compare to expected performance
        expected_time = 30  # We want under 30 seconds
        if fast_time < expected_time:
            improvement = expected_time / fast_time
            print(f"🚀 {improvement:.1f}x faster than target!")
        else:
            print(f"⚠️  {fast_time/expected_time:.1f}x slower than target")
        
        print("\n💡 OPTIMIZATION STATUS:")
        if fast_time < 20:
            print("🎉 ULTRA-FAST! Ready for production")
        elif fast_time < 40:
            print("✅ FAST! Good for regular use")
        elif fast_time < 80:
            print("⚠️  MODERATE! Consider further optimization")
        else:
            print("❌ SLOW! Needs more optimization")
            
        return True
    else:
        print("❌ Test failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
