#!/usr/bin/env python3
"""
Test to verify image functionality has been removed and performance improved
"""

import logging
import sys
import time
from amazon_scraper.scraper import AmazonScraper
from amazon_scraper.models import Product

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_product_model():
    """Test that Product model no longer has image_url"""
    print("🧪 Testing Product model...")
    
    try:
        # Try to create a product without image_url
        product = Product(
            title="Test Product",
            url="https://example.com",
            asin_code="B123456789",
            price="29,99"
        )
        
        # Check that image_url is not in the model
        if hasattr(product, 'image_url'):
            print("❌ Product model still has image_url field")
            return False
        else:
            print("✅ Product model successfully removed image_url field")
            return True
            
    except Exception as e:
        print(f"❌ Error testing Product model: {str(e)}")
        return False

def test_scraper_speed():
    """Test scraper speed without image processing"""
    print("\n🚀 Testing scraper speed without images...")
    
    try:
        scraper = AmazonScraper(use_proxy=False)
        test_url = "https://www.amazon.it/s?k=laptop&ref=nb_sb_noss"
        
        start_time = time.time()
        
        # Test just driver initialization and basic page load
        driver = scraper._init_chrome_driver()
        driver.get(test_url)
        
        # Quick test - find some products
        from selenium.webdriver.common.by import By
        product_elements = driver.find_elements(By.XPATH, "//div[@data-component-type='s-search-result']")
        
        driver.quit()
        end_time = time.time()
        
        duration = end_time - start_time
        
        print(f"✅ Basic scraper test completed")
        print(f"⏱️  Time: {duration:.2f} seconds")
        print(f"📦 Found {len(product_elements)} product elements")
        
        if duration < 15:
            print("🎉 EXCELLENT! Very fast initialization")
        elif duration < 30:
            print("✅ GOOD! Fast initialization")
        else:
            print("⚠️  SLOW! Initialization took too long")
        
        return True
        
    except Exception as e:
        print(f"❌ Scraper speed test failed: {str(e)}")
        return False

def test_database_schema():
    """Test that database no longer expects image_url"""
    print("\n🗄️  Testing database schema...")
    
    try:
        from amazon_scraper.db_helper import DatabaseHelper
        
        db = DatabaseHelper()
        
        # Create a test product
        product = Product(
            title="Test Product",
            url="https://example.com",
            asin_code="TEST123",
            price="19,99"
        )
        
        # Try to save it (should work without image_url)
        db.save_product(product)
        
        print("✅ Database successfully handles products without image_url")
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {str(e)}")
        return False

def main():
    print("Amazon Scraper - Image Removal Verification")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Product model
    if test_product_model():
        tests_passed += 1
    
    # Test 2: Scraper speed
    if test_scraper_speed():
        tests_passed += 1
    
    # Test 3: Database schema
    if test_database_schema():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    print(f"Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Image functionality successfully removed")
        print("🚀 Scraper should be faster without image processing")
        return True
    else:
        print("❌ Some tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
