#!/usr/bin/env python3
"""
Test script to verify proxy functionality fixes in the Amazon scraper.
"""

import logging
import sys
from amazon_scraper.scraper import AmazonScraper

# Configure logging to see what's happening
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_scraper():
    """Test the scraper with a simple Amazon search"""
    
    # Create scraper instance
    scraper = AmazonScraper()
    
    # Test URL - a simple search for laptops
    test_url = "https://www.amazon.it/s?k=laptop&ref=nb_sb_noss"
    
    print("Testing Amazon scraper with proxy fixes...")
    print(f"URL: {test_url}")
    print("-" * 50)
    
    try:
        # Test scraping just 1 page to verify functionality
        products = scraper.scrape_amazon_page(test_url, max_pages=1)
        
        if products:
            print(f"✅ Success! Found {len(products)} products")
            print("\nFirst few products:")
            for i, product in enumerate(products[:3]):
                print(f"{i+1}. {product.title[:50]}... - {product.price}")
        else:
            print("❌ No products found")
            
    except Exception as e:
        print(f"❌ Error occurred: {str(e)}")
        return False
    
    return True

def test_proxy_validation():
    """Test proxy validation functionality"""
    print("\nTesting proxy validation...")
    print("-" * 30)
    
    scraper = AmazonScraper()
    
    # Test getting a working proxy
    working_proxy = scraper._get_working_proxy()
    
    if working_proxy:
        print(f"✅ Found working proxy: {working_proxy}")
    else:
        print("⚠️  No working proxies found (this might be normal)")
    
    return True

if __name__ == "__main__":
    print("Amazon Scraper Proxy Fix Test")
    print("=" * 40)
    
    # Test proxy validation first
    test_proxy_validation()
    
    # Test main scraping functionality
    success = test_scraper()
    
    if success:
        print("\n✅ All tests completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Tests failed!")
        sys.exit(1)
