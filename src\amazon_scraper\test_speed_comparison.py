#!/usr/bin/env python3
"""
Speed comparison test - Fast mode vs Proxy mode
"""

import logging
import sys
import time
from amazon_scraper.scraper import AmazonScraper

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_fast_mode():
    """Test scraper in fast mode (no proxy)"""
    print("🚀 Testing FAST MODE (no proxy, regular selenium)")
    print("-" * 50)
    
    scraper = AmazonScraper(use_proxy=False)  # Fast mode
    test_url = "https://www.amazon.it/s?k=laptop&ref=nb_sb_noss"
    
    try:
        start_time = time.time()
        products = scraper.scrape_amazon_page(test_url, max_pages=1)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if products:
            print(f"✅ Fast mode: Found {len(products)} products")
            print(f"⏱️  Time: {duration:.2f} seconds")
            print(f"📊 Speed: {len(products)/duration:.1f} products/second")
            return duration, len(products)
        else:
            print("❌ No products found in fast mode")
            return None, 0
            
    except Exception as e:
        print(f"❌ Fast mode failed: {str(e)}")
        return None, 0

def test_proxy_mode():
    """Test scraper in proxy mode"""
    print("\n🔒 Testing PROXY MODE (selenium-wire with proxy)")
    print("-" * 50)
    
    scraper = AmazonScraper(use_proxy=True)  # Proxy mode
    test_url = "https://www.amazon.it/s?k=laptop&ref=nb_sb_noss"
    
    try:
        start_time = time.time()
        products = scraper.scrape_amazon_page(test_url, max_pages=1)
        end_time = time.time()
        
        duration = end_time - start_time
        
        if products:
            print(f"✅ Proxy mode: Found {len(products)} products")
            print(f"⏱️  Time: {duration:.2f} seconds")
            print(f"📊 Speed: {len(products)/duration:.1f} products/second")
            return duration, len(products)
        else:
            print("❌ No products found in proxy mode")
            return None, 0
            
    except Exception as e:
        print(f"❌ Proxy mode failed: {str(e)}")
        return None, 0

def main():
    print("Amazon Scraper Speed Comparison Test")
    print("=" * 50)
    
    # Test fast mode first
    fast_time, fast_products = test_fast_mode()
    
    # Test proxy mode
    proxy_time, proxy_products = test_proxy_mode()
    
    # Compare results
    print("\n" + "=" * 50)
    print("📊 COMPARISON RESULTS")
    print("=" * 50)
    
    if fast_time and proxy_time:
        speed_improvement = proxy_time / fast_time
        print(f"🚀 Fast mode: {fast_time:.2f}s ({fast_products} products)")
        print(f"🔒 Proxy mode: {proxy_time:.2f}s ({proxy_products} products)")
        print(f"⚡ Speed improvement: {speed_improvement:.1f}x faster in fast mode")
        
        if speed_improvement > 2:
            print("🎉 Significant speed improvement achieved!")
        elif speed_improvement > 1.5:
            print("✅ Good speed improvement")
        else:
            print("⚠️  Minimal speed difference")
    
    elif fast_time:
        print(f"🚀 Fast mode: {fast_time:.2f}s ({fast_products} products)")
        print("🔒 Proxy mode: Failed")
        print("✅ Fast mode is working, proxy mode has issues")
    
    elif proxy_time:
        print("🚀 Fast mode: Failed")
        print(f"🔒 Proxy mode: {proxy_time:.2f}s ({proxy_products} products)")
        print("⚠️  Only proxy mode is working")
    
    else:
        print("❌ Both modes failed")
        return False
    
    print("\n💡 RECOMMENDATION:")
    if fast_time and fast_time < 30:
        print("Use fast mode (use_proxy=False) for maximum speed")
        print("Only use proxy mode when you need to avoid IP blocking")
    else:
        print("Consider further optimizations or check network connectivity")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
